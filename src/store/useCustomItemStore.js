import { create } from 'zustand';

const initialState = {
  modalOpen: false,
  customItem: {
    name: '',
    description: '',
    priceExcGST: '',
    priceIncGST: '',
    isGSTFree: false,
    saveForLater: false,
    quantity: 1,
    note: '',
  },
};

const useCustomItemStore = create((set, get) => ({
  ...initialState,
  setModalOpen: (open) => set({ modalOpen: open }),
  setCustomItem: (field, value) =>
    set((state) => ({
      customItem: {
        ...state.customItem,
        [field]: value,
      },
    })),
  resetCustomItem: () => set({ customItem: initialState.customItem }),
  getMarkedUpPrice: () => {
    const { customItem } = get();
    // Use the GST inclusive price if available, otherwise calculate from exclusive
    if (customItem.priceIncGST) {
      return parseFloat(customItem.priceIncGST).toFixed(2);
    }
    const basePrice = parseFloat(customItem.priceExcGST) || 0;
    return (basePrice * 1.1).toFixed(2);
  },
}));

export default useCustomItemStore;
