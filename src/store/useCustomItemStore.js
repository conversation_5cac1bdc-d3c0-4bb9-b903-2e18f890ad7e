import { create } from 'zustand';

const initialState = {
  modalOpen: false,
  customItem: {
    name: '',
    description: '',
    priceExcGST: '',
    priceIncGST: '',
    isGSTFree: false,
    saveForLater: false,
    quantity: 1,
    note: '',
  },
};

const useCustomItemStore = create((set, get) => ({
  ...initialState,
  setModalOpen: (open) => set({ modalOpen: open }),
  setCustomItem: (field, value) =>
    set((state) => ({
      customItem: {
        ...state.customItem,
        [field]: value,
      },
    })),
  resetCustomItem: () => set({ customItem: initialState.customItem }),
  getMarkedUpPrice: (markup = 0) => {
    const { customItem } = get();
    const basePrice = parseFloat(customItem.priceExcGST) || 0;

    if (customItem.isGSTFree) {
      // For GST free items, apply markup to the base price
      return (basePrice * (1 + markup / 100)).toFixed(2);
    }

    // For regular items, use the GST inclusive price if available, otherwise calculate from exclusive
    let finalPrice;
    if (customItem.priceIncGST) {
      finalPrice = parseFloat(customItem.priceIncGST);
    } else {
      finalPrice = basePrice * 1.1;
    }

    // Apply markup to the final price
    return (finalPrice * (1 + markup / 100)).toFixed(2);
  },
}));

export default useCustomItemStore;
