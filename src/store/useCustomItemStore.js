import { create } from 'zustand';

const initialState = {
  modalOpen: false,
  customItem: {
    name: '',
    description: '',
    priceExcGST: '',
    priceIncGST: '',
    isGSTFree: false,
    saveForLater: false,
    quantity: 1,
    note: '',
  },
};

const useCustomItemStore = create((set, get) => ({
  ...initialState,
  setModalOpen: (open) => set({ modalOpen: open }),
  setCustomItem: (field, value) =>
    set((state) => ({
      customItem: {
        ...state.customItem,
        [field]: value,
      },
    })),
  resetCustomItem: () => set({ customItem: initialState.customItem }),
}));

export default useCustomItemStore;
