@import "Accordion";
@import "Docket";
@import "DocketOrderLine";
@import "MajorDeliveryModal";
@import "RecurringOrderForm";
@import "SearchMenuItems";


.supplier-show-container {
  background: #f9f9f9;
  @include media-down(small-tablet) {
    padding: 0 12px;
  }
}
.supplier-details-container {
  width: 70%;
  z-index: 1;
  margin-top: 2rem;
  margin-bottom: 3rem;
}
.supplier-show-wrapper {
  display: flex;
  margin: 0 auto;
  max-width: 1400px;
  padding: 0 10px;
  @include media-down(large-tablet) {
    padding: 0;
  }
}
.supplier-show-heading-block {
  width: 100%;
  height: 323px;
  background: white;
  position: absolute;
  z-index: -1;
  &.for-dash {
    width: initial;
  }
}
.supplier-banner {
  display: flex;
  max-width: 1200px;
  margin: 0 auto 20px;
  &-image {
    flex: 1;
    max-width: 400px;
    height: 220px;
  }
  img {
    object-fit: cover;
  }
  &-navigation {
    line-height: 1;
    &__link {
      color: $primary;
      font-family: 'Museo Slab', sans-serif;
    }
  }
  &-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0 20px;
    h1 {
      font-size: 32px;
      margin-bottom: 0;
      padding: 4px 0;
    }
    p {
      margin: 0;
      font-size: 14px;
      color: #555555;
    }
    .supplier-icons {
      margin-top: 3px;
      .supplier-icon {
        border-radius: 50%;
        border: 1px solid;
        padding: 4px;
        &:before {
          width: 18px;
          height:18px;
        }
      }
    }
  }
}

.liquor-licence-warning {
  margin: -6px 0 2px;
  color: gray;
  font-size: 12px;
}

.supplier-menu-banner-wrapper {
  position: sticky;
  top: 60px;
  z-index: 10;
  background: white;
  border: 1px solid #e3e3e3;
  border-right: none;
  &.loading {
    padding: 25px 0;
  }
  @include media-down(tablet) {
    // account for no search bar on mobile
    margin-left: -12px;
    padding-left: 12px;
    margin-right: -12px;
    padding-right: 12px;
  }
}
.supplier-menu-banner {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  height: 50px;
  font-size: 15px;
  padding: 0 6px;
  &.aisle {
    border-bottom:1px solid #e3e3e3
  }
  .active-menu-section > a {
    text-decoration: none;
    color: $white;
    background: $off-black;
  }
}
.supplier-categories {
  display: flex;
  align-items: center;
  flex-grow: 1;
  height: 100%;
  background: $white;
  white-space: nowrap;
  overflow: hidden;
  li {
    display: inline-block;
    a {
      padding: 8px 12px;
      border-radius: 4px;
    }
    a:hover {
      text-decoration: none;
      color: $white;
      background: $off-black;
    }
  }
}

.menu-section-wrapper {
  background: #f9f9f9;
}
.menu-sections {
  max-width: 1200px;
  margin: 0 auto 0;
  .menu-section {
    padding: 30px 16px 30px 0;
    scroll-margin: 40px;
    &-title {
      margin-bottom: 20px;
      font-size: 24px;
      &.with-clear {
        display: flex;
      }
    }
    &-items{
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      grid-gap: 16px;
    }
  }
}

.menu-section-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  min-height: 180px;
  background: white;
  border: 1px solid #e3e3e3;
  border-radius: 2px;
  padding: 10px;
  scroll-margin-top: 60px;
  cursor: pointer;
  &:hover {
    border: 1px solid #c3c3c3;
    box-shadow: 0px 22px 24px 0px rgba(36, 28, 21, 0.08);
  }
  &.over-budget {
    position: relative;
    opacity: 0.5;
  }
  &-title-and-add-btn {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    height: 30px;
    span {
      display: inline-flex;
      margin-left: 4px;
    }
  }
  &-description-and-image {
    display: flex;
    flex: 1;
  }
  &-description {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 14px;
    &.no-flex {
      justify-content: normal;
    }
    p {
      display: -webkit-box;
      -webkit-line-clamp: 5;
      -webkit-box-orient: vertical;
      overflow: hidden;
      margin-bottom: 0;
      line-height: 16px;
      font-size: 13px;
      color: #656464;
      word-break: break-word;
    }
  }
  &-image {
    flex: 1;
    margin-left: 10px;
    max-width: 110px;
    img {
      border-radius: 2px;
      object-fit: contain;
    }
  }
  .letter-icon {
    display: inline-block;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    line-height: 22px;
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    color: black;
    margin-right: 5px;
    opacity: 0.4;
    &.VE {
      background: $green;
    }
    &.V {
      background: lighten($green-darker, 10%);
    }
    &.GF {
      background: $yellow;
    }
    &.IP {
      background: $purple;
    }
    &.DF {
      background: $blue-darker;
    }
    &.EF {
      background: $burgundy;
    }
    &.H {
      background: $teal;
    }
    &.K {
      background: $teal-darker;
    }
    &.NF {
      background: $purple-darker;
    }
  }
  .dietaries-container {
    display: flex;
  }
  &-price-and-quantity {
    display: flex;
    justify-content: space-between;
    line-height: 1;
    font-size: 14px;
    margin-top: 10px;
    font-weight: bold;
  }
  .rate-card-price {
    color: $error;
    &.strike {
      text-decoration: line-through;
    }
  }
  h4 {
    margin-bottom: 6px;
    text-transform: capitalize;
    font-size: 15px;
    font-family: $body-font;
  }
}

.reduced {
  color: #008000;
}

.special-checkout-price {
  align-self: baseline;
  margin-left: 8px;
  border: 1px solid #008000;
  color: #008000;
  padding: 2px 4px;
  font-size: 12px;
  border-radius: 4px;
  margin-bottom: 4px;
  font-weight: 700;
  text-transform: uppercase;
}

.special-label {
  border: 2px solid #008000;
  color: #008000;
  margin-right: 0.5rem;
  font-weight: $font-weight-boldest;
  padding: 2px 4px;
  font-size: 11px;
}
.special-price {
  color: #008000;
}
.orderline-name-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.note-buttons {
  text-align: right;
  button + button {
    margin-left: 8px;
  }
}

.supplier-show-container {
  &.for-dash {
    min-height: 100vh;
    .supplier-menu-banner-wrapper {
      top: 53px;
    }
    .docket {
      top: 0px;
      &.with-meal-note {
        top: 55px;
      }
    }
    .menu-sections .menu-section-items {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }
  &.loading {
    .supplier-banner {
      &-image {
        width: 400px;
        height: 220px;
        object-fit: cover;
        @include shimmer;
        @include media-down(small-tablet) {
          width: 100%;
          margin-bottom: 2 0px;
        }
      }
      &-heading {
        height: 40px;
        width: 400px;
        margin-bottom: 20px;
        @include shimmer;
        @include media-down(small-tablet) {
          width: 100%;
        }
      }
      &-details-info {
        height: 20px;
        width: 300px;
        margin-bottom: 20px;
        @include shimmer;
        @include media-down(small-tablet) {
          width: 100%;
        }
      }
      .supplier-icons {
        height: 20px;
        width: 300px;
        margin-bottom: 20px;
        @include shimmer;
        @include media-down(small-tablet) {
          width: 100%;
        }
      }
    }
  }
}

.menu-sections.loading {
  .menu-section {
    &-title {
    width: 30vw;
    height: 40px;
    @include shimmer;
    }
    &-item {
      @include shimmer;
    }
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  padding: 20px 46px 8px 24px;
  &.sticky {
    box-shadow: rgba(0, 0, 0, 0.2) 0px calc(1px) 15px;
    border-bottom: 1px solid rgb(231, 231, 231);
  }
}

.menu-item-modal {
  display: grid;
  grid-template-columns: 1fr 50px 100px;
  grid-column-gap: 15px;
  align-items: center;
  margin-bottom: 0.8125rem;
  &-details {
    position: relative;
    max-height: 500px;
    overflow-y: scroll;
    padding: 8px 24px 16px;
  }
  &-title {
    font-family: $body-font;
    font-size: 23px;
    text-transform: capitalize;
  }
  &-description {
    margin-bottom: 30px;
    color: #828585;
  }
  &-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    margin-bottom: 30px;
  }
  &-button {
    position: relative;
    max-width: 100%;
    margin: 0px;
    display: inline-flex;
    min-height: 30px;
    border-radius: 30px;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    user-select: none;
    text-decoration: none;
    text-align: center;
    background-color: transparent;
    box-shadow: transparent 0px 0px 0px 1px inset;
    width: 30px;
    height: 30px;
    padding: 0px;
    align-items: center;
    justify-content: center;
    &:hover {
      background-color: rgb(247, 247, 247);
      box-shadow: transparent 0px 0px
    }
  }
  &-option-container {
    padding: 10px 0px;
    border-bottom: 1px solid #d6d6d6;
  }
  &-option {
    display: grid;
    grid-template-columns: 1fr 60px 60px 100px;
    grid-column-gap: 15px;
    align-items: center;
    &-name {
      font-weight: bold;
    }
    &-price {
      white-space: nowrap;
      &--custom {
        color: red;
      }
      &.over-budget {
        color: red;
      }
    }
    @include media-down(large-mobile) {
      grid-template-columns: 1fr 1fr 1fr 1fr;
      grid-gap: 10px;
      &-name {
        grid-column: 1/4;
      }

      &-price {
        text-align: right;
      }
    }
  }
  &-add-note {
    cursor: pointer;
    text-transform: uppercase;
    margin-bottom: 0;
    text-decoration: underline;
    font-weight: bold;
    font-size: 11px;
    color: $black;
    margin-right: 0;
    @include media-down(large-mobile) {
      grid-column: 1/3;
    }
  }
  &-note {
    max-height: 0;
    height: 10px;
    margin: 0.8125rem 0;
    border: 1px solid #c8c8c8;
    padding: 20px 8px;
    font-size: 14px;
  }
  &-extras {
    margin-top:20px;
  }
  &-extras-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 8px;
    margin-bottom: 40px;
    .button {
      font-size: 12px;
      @include media-down(large-mobile) {
        padding: 6px 10px;
      }
      &.disabled {
        pointer-events: none;
        background: #edecec;
        color: rgb(189, 187, 187);
      }
    }
  }
  &-extra-title {
    margin-bottom: 10px;
  }
  &-extra-price {
    margin-left: 10px;
    color: #FB35DC;
  }
  &-input {
    display: flex;
    align-items: center;
    input {
      width: 50%;
      height: 30px;
      text-align: center;
      padding: 4px 10px;
      &[type="number"] {
        width: 50px;
      }
    }
    .button {
      padding: 0 8px;
    }
    @include media-down(large-mobile) {
      grid-column: 3/5;
      justify-content: flex-end;
    }
  }
  &-add {
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px 20px;
    &.padded {
      padding-top: 16px;
    }
    p {
      color: $primary;
      font-weight: bold;
    }
    &.shadow {
      padding-top: 16px;
      box-shadow: rgba(0, 0, 0, 0.2) 0px calc(6px) 15px;
    }
    .button {
      display: block;
      font-size: 16px;
      padding: 8px 16px;
      background: $black;
      border: none;
      margin-left: auto;
      &.disabled {
        pointer-events: none;
        background: #e5e5e5;
      }
    }
  }
  &-total {
    border-top: 1px solid;
    padding-top: 14px;
    text-align: center;
    p {
      font-weight: bold;
      margin-top: 10px;
    }
  }
}

.order-again-modal-line {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e5e5e5;
  p {
    margin-bottom: 0;
    margin-left: 8px;
  }
  &__image {
    margin-right: 10px;
  }
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
    background-color: #f5f5f5;
    .set-orderline {
      cursor: not-allowed;
      &.disabled {
        opacity: 0.3;
      }
    }
  }
}

.quantity-warning {
  font-style: italic;
  font-size: 14px;
  text-align: right;
  p {
    margin: 4px 0;
  }
}

@include media-down (tablet) {
  .supplier-details-container {
    width: 100%;
    margin-top: 1rem;
  }
  .supplier-banner {
    flex-direction: column;
    &-navigation {
      margin-top: 10px;
    }
    &-details {
      padding: 0;
      h1 {
        margin-bottom: 4px;
      }
    }
    &-image {
      max-width: 100%;
    }
  }
  .supplier-categories {
    margin-right: -20px;
  }
  .supplier-show-heading-block {
    display: none;
  }
  .menu-sections {
    padding: 0;
  }
}

.clear-searched-items {
  margin-left: 20px;
  background: black;
  color: white;
  padding: 0px 20px;
  border-radius: 14px;
  text-transform: uppercase;
  cursor: pointer;
  font-size: 14px;
}

.menu-item-serving-over-budget {
  color: red;
  font-size: 14px;
  font-weight: normal;
}

// Custom menu item styles for admin users
.custom-menu-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
  background: white;
  border: 2px dashed #c3c3c3;
  border-radius: 2px;
  padding: 0;

  .custom-menu-item-button {
    width: 100%;
    height: 100%;
    min-height: 180px;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 20px;
  }

  &:hover {
    border-color: $primary;
  }

  .custom-menu-item-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #666;

    .custom-menu-item-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      font-size: 24px;
      font-weight: bold;
      color: #999;
    }

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #666;
    }
  }

  &:hover .custom-menu-item-content {
    .custom-menu-item-icon {
      background: $primary;
      color: white;
    }

    h4 {
      color: $primary;
    }
  }
}

.menu-modal {
  overflow-y: auto;

  h4 {
    margin: 20px 0 10px 0;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
  }

  .custom-item-section {
    margin-bottom: 20px;

    input {
      margin: 0;
    }

    label {
      display: block;
      margin-bottom: 15px;

      input, textarea {
        margin-top: 5px;
      }
    }

    .price-fields {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 15px;
      max-width: 500px;

      label {
        margin-bottom: 0;
      }
    }
  }

  .price-display {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;

    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }

  .checkbox-container {
    display: flex;
    align-items: center;
    margin: 10px 0;

    input[type="checkbox"] {
      margin-right: 8px;
    }

    label {
      margin: 0;
      cursor: pointer;
      font-size: 14px;
    }
  }

  .custom-item-modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
    margin-top: 20px;

    .total-price {
      font-size: 16px;
      color: $primary;
    }

    .button {
      background: $primary;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &.disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      &:hover:not(.disabled) {
        background: darken($primary, 10%);
      }
    }
  }
}

.meal-plan-section-tag {
  background: $highlight;
  border-radius: 4px;
  padding: 2px 6px;
  color: white;
  display: inline-block;
  margin-left: 8px;
  font-size: 12px;
}

.set-orderline {
  display: inline-block;
  margin-left: auto;
  cursor: pointer;
  padding-left: 35px;
  margin-top: 12px;
  margin-bottom: 14px;
  background: url(../../../images/icons/icon-checkbox-empty.svg) 0% 50% no-repeat;
  width: 24px;
  height: 24px;
  &.checked {
    background: url(../../../images/icons/icon-checkbox-checked.svg) 0% 50% no-repeat;
  }
  input[type="checkbox"] {
    position: absolute;
    margin: 0;
    clip: rect(0, 0, 0, 0);
  }
}

.no-orderline-image {
  width: 44px;
  height: 44px;
  object-fit: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  background: black;
  color: white;
}