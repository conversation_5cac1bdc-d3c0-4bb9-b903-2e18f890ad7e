import { useState, useContext } from 'react';
import { Modal } from 'react-responsive-modal';
import 'react-responsive-modal/styles.css';

import useCustomItemStore from 'store/useCustomItemStore';
import useDocketStore from 'store/useDocketStore';
import { UserContext } from 'context/user';

const CustomItemModal = ({ supplierID }) => {
  const [loading, setLoading] = useState(false);
  const { user } = useContext(UserContext);

  const { modalOpen, customItem, setModalOpen, setCustomItem, resetCustomItem, getMarkedUpPrice } = useCustomItemStore(
    (state) => ({
      modalOpen: state.modalOpen,
      customItem: state.customItem,
      setModalOpen: state.setModalOpen,
      setCustomItem: state.setCustomItem,
      resetCustomItem: state.resetCustomItem,
      getMarkedUpPrice: state.getMarkedUpPrice,
    })
  );

  const { addOrderLineToCart } = useDocketStore((state) => ({
    addOrderLineToCart: state.addOrderLineToCart,
  }));

  const closeModal = () => {
    setModalOpen(false);
    resetCustomItem();
  };

  const handleInputChange = (field, value) => {
    setCustomItem(field, value);
  };

  const handlePriceChange = (field, value) => {
    const numericValue = parseFloat(value) || 0;

    if (field === 'priceExcGST') {
      // When exc GST changes, calculate inc GST (+10%)
      const incGST = (numericValue * 1.1).toFixed(2);
      setCustomItem('priceExcGST', value);
      setCustomItem('priceIncGST', incGST);
    } else if (field === 'priceIncGST') {
      // When inc GST changes, calculate exc GST (-10%)
      const excGST = (numericValue / 1.1).toFixed(2);
      setCustomItem('priceIncGST', value);
      setCustomItem('priceExcGST', excGST);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!customItem.name || (!customItem.priceExcGST && !customItem.priceIncGST)) {
      return;
    }

    setLoading(true);

    try {
      const markedUpPrice = getMarkedUpPrice();

      // Create a custom order line object in the format expected by the API
      const finalPrice = customItem.isGSTFree ? parseFloat(customItem.priceExcGST) : parseFloat(markedUpPrice);

      const customOrderLine = {
        // For custom items, we'll use a special format that the backend can recognize
        custom_item: {
          name: customItem.name,
          description: customItem.description,
          price: finalPrice,
          is_gst_free: customItem.isGSTFree,
        },
        quantity: customItem.quantity,
        note: customItem.note,
        company_id: user?.profile?.company_id,
        supplier_id: supplierID,
      };

      // Add to cart using the existing docket store method
      await addOrderLineToCart([customOrderLine]);

      if (customItem.saveForLater) {
        // TODO: Implement save for later functionality
        console.log('Save for later functionality to be implemented');
      }

      closeModal();
    } catch (error) {
      console.error('Error adding custom item:', error);
      // Show user-friendly error message
      alert('There was an error adding the custom item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const totalPrice = (parseFloat(getMarkedUpPrice()) * customItem.quantity).toFixed(2);

  return (
    <Modal
      open={modalOpen}
      onClose={closeModal}
      center
      className="custom-item-modal"
      styles={{
        modal: { minWidth: '500px', padding: 0, borderRadius: '10px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading">Add Custom Item</h3>

        <form onSubmit={handleSubmit}>
          <div className="custom-item-section">
            <h4>Item Details</h4>

            <label>
              Name *
              <input
                type="text"
                value={customItem.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                placeholder="Enter item name"
              />
            </label>

            <label>
              Description
              <textarea
                value={customItem.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter item description"
                rows="3"
                style={{ marginBottom: 0 }}
              />
            </label>

            <div className="price-fields">
              <label>
                Price (Exc GST) *
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={customItem.priceExcGST}
                  onChange={(e) => handlePriceChange('priceExcGST', e.target.value)}
                  required
                  placeholder="0.00"
                />
              </label>

              <label>
                Price (Inc GST) *
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={customItem.priceIncGST}
                  onChange={(e) => handlePriceChange('priceIncGST', e.target.value)}
                  required
                  placeholder="0.00"
                />
              </label>
            </div>

            <div className="checkbox-container">
              <input
                type="checkbox"
                id="isGSTFree"
                checked={customItem.isGSTFree}
                onChange={(e) => handleInputChange('isGSTFree', e.target.checked)}
              />
              <span className="custom-checkbox" />
              <label htmlFor="isGSTFree">GST Free</label>
            </div>

            <div className="checkbox-container">
              <input
                type="checkbox"
                id="saveForLater"
                checked={customItem.saveForLater}
                onChange={(e) => handleInputChange('saveForLater', e.target.checked)}
              />
              <span className="custom-checkbox" />
              <label htmlFor="saveForLater">Save for Later</label>
            </div>
          </div>

          {/* Order Line Section */}
          <div className="custom-item-section">
            <h4>Order Details</h4>

            <label>
              Quantity
              <input
                type="number"
                min="1"
                value={customItem.quantity}
                onChange={(e) => handleInputChange('quantity', parseInt(e.target.value, 10) || 1)}
              />
            </label>

            <label>
              Note
              <textarea
                value={customItem.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                placeholder="Add any special instructions"
                rows="2"
              />
            </label>
          </div>

          <div className="custom-item-modal-footer">
            <div className="total-price">
              <strong>Total: ${totalPrice}</strong>
            </div>
            <button
              type="submit"
              className={`button ${!customItem.name || (!customItem.priceExcGST && !customItem.priceIncGST) || loading ? 'disabled' : ''
                }`}
              disabled={!customItem.name || (!customItem.priceExcGST && !customItem.priceIncGST) || loading}
            >
              {loading ? 'Adding...' : 'Add to Cart'}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CustomItemModal;
