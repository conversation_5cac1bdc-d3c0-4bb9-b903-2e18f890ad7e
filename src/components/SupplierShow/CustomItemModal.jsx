import { useState, useContext } from 'react';
import { useSelector } from 'react-redux';
import { Modal } from 'react-responsive-modal';
import 'react-responsive-modal/styles.css';

import useCustomItemStore from 'store/useCustomItemStore';
import useDocketStore from 'store/useDocketStore';
import { UserContext } from 'context/user';

const CustomItemModal = ({ supplierID }) => {
  const [loading, setLoading] = useState(false);
  const { user } = useContext(UserContext);

  // Get supplier data from Redux to access markup
  const supplierListing = useSelector((state) => state.suppliers.listing);
  const markup = supplierListing?.markup ? parseFloat(supplierListing.markup) : 0;

  const { modalOpen, customItem, setModalOpen, setCustomItem, resetCustomItem } = useCustomItemStore((state) => ({
    modalOpen: state.modalOpen,
    customItem: state.customItem,
    setModalOpen: state.setModalOpen,
    setCustomItem: state.setCustomItem,
    resetCustomItem: state.resetCustomItem,
  }));

  const { addOrderLineToCart } = useDocketStore((state) => ({
    addOrderLineToCart: state.addOrderLineToCart,
  }));

  const closeModal = () => {
    setModalOpen(false);
    resetCustomItem();
  };

  const handleInputChange = (field, value) => {
    setCustomItem(field, value);
  };

  const handlePriceChange = (field, value) => {
    const numericValue = parseFloat(value) || 0;

    if (customItem.isGSTFree) {
      // For GST free items, only update the single price field
      setCustomItem('priceExcGST', value);
      setCustomItem('priceIncGST', ''); // Clear inc GST for GST free items
    } else if (field === 'priceExcGST') {
      // When exc GST changes, calculate inc GST (+10%)
      const incGST = (numericValue * 1.1).toFixed(2);
      setCustomItem('priceExcGST', value);
      setCustomItem('priceIncGST', incGST);
    } else if (field === 'priceIncGST') {
      // When inc GST changes, calculate exc GST (-10%)
      const excGST = (numericValue / 1.1).toFixed(2);
      setCustomItem('priceIncGST', value);
      setCustomItem('priceExcGST', excGST);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!customItem.name || (!customItem.priceExcGST && !customItem.priceIncGST)) {
      return;
    }

    setLoading(true);

    try {
      // Send the price excluding GST to the API (API will handle markup)
      const finalPrice = parseFloat(customItem.priceExcGST);

      const customOrderLine = {
        custom_item: {
          name: customItem.name,
          description: customItem.description,
          price: finalPrice,
          is_gst_free: customItem.isGSTFree,
          save_for_later: customItem.saveForLater,
        },
        quantity: customItem.quantity,
        note: customItem.note,
        company_id: user?.profile?.company_id,
        supplier_id: supplierID,
      };

      // Add to cart using the existing docket store method
      await addOrderLineToCart([customOrderLine]);

      if (customItem.saveForLater) {
        // TODO: Implement save for later functionality
        console.log('Save for later functionality to be implemented');
      }

      closeModal();
    } catch (error) {
      console.error('Error adding custom item:', error);
      // Show user-friendly error message
      alert('There was an error adding the custom item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Calculate marked up price excluding GST for display
  const calculateMarkedUpPriceExcGST = () => {
    const basePriceExcGST = parseFloat(customItem.priceExcGST) || 0;
    return (basePriceExcGST * (1 + markup / 100)).toFixed(2);
  };

  // Calculate total price for display in button - should be marked up price exc GST
  const markedUpPriceExcGST = calculateMarkedUpPriceExcGST();
  const totalPrice = (parseFloat(markedUpPriceExcGST) * customItem.quantity).toFixed(2);

  return (
    <Modal
      open={modalOpen}
      onClose={closeModal}
      center
      className="custom-item-modal"
      styles={{
        modal: { minWidth: '500px', padding: 0, borderRadius: '10px' },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading">Add Custom Item</h3>

        <form onSubmit={handleSubmit}>
          <div className="custom-item-section">
            <h4>Item Details</h4>

            <label>
              Name *
              <input
                type="text"
                value={customItem.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
                placeholder="Enter item name"
              />
            </label>

            <label>
              Description
              <textarea
                value={customItem.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter item description"
                rows="3"
                style={{ marginBottom: 0 }}
              />
            </label>

            <div className="price-fields">
              {customItem.isGSTFree ? (
                // Show only single price input for GST free items
                <label>
                  Price *
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={customItem.priceExcGST}
                    onChange={(e) => handlePriceChange('priceExcGST', e.target.value)}
                    required
                    placeholder="0.00"
                  />
                </label>
              ) : (
                // Show both price inputs for regular items
                <>
                    <label>
                      Price (Exc GST) *
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={customItem.priceExcGST}
                        onChange={(e) => handlePriceChange('priceExcGST', e.target.value)}
                        required
                        placeholder="0.00"
                      />
                    </label>

                    <label>
                      Price (Inc GST) *
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={customItem.priceIncGST}
                        onChange={(e) => handlePriceChange('priceIncGST', e.target.value)}
                        required
                        placeholder="0.00"
                      />
                    </label>
                </>
              )}

              {/* Markup price field - non-editable, updates automatically */}
              {markup > 0 && (
                <label>
                  Mark Up (Exc GST) ({markup}%)
                  <input
                    type="text"
                    value={`$${calculateMarkedUpPriceExcGST()}`}
                    readOnly
                    style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
                  />
                </label>
              )}
            </div>

            <div className="checkbox-container">
              <input
                type="checkbox"
                id="isGSTFree"
                checked={customItem.isGSTFree}
                onChange={(e) => handleInputChange('isGSTFree', e.target.checked)}
              />
              <span className="custom-checkbox" />
              <label htmlFor="isGSTFree">GST Free</label>
            </div>

            <div className="checkbox-container">
              <input
                type="checkbox"
                id="saveForLater"
                checked={customItem.saveForLater}
                onChange={(e) => handleInputChange('saveForLater', e.target.checked)}
              />
              <span className="custom-checkbox" />
              <label htmlFor="saveForLater">Save for Later</label>
            </div>
          </div>

          {/* Order Line Section */}
          <div className="custom-item-section">
            <h4>Order Details</h4>

            <label>
              Quantity
              <input
                type="number"
                min="1"
                value={customItem.quantity}
                onChange={(e) => handleInputChange('quantity', parseInt(e.target.value, 10) || 1)}
              />
            </label>

            <label>
              Note
              <textarea
                value={customItem.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                placeholder="Add any special instructions"
                rows="2"
              />
            </label>
          </div>

          <div className="custom-item-modal-footer ">
            <button
              type="submit"
              className={`button ${!customItem.name || (!customItem.priceExcGST && !customItem.priceIncGST) || loading ? 'disabled' : ''
                }`}
              disabled={!customItem.name || (!customItem.priceExcGST && !customItem.priceIncGST) || loading}
            >
              <span>{loading ? 'Adding...' : 'Add to Cart -'}</span>
              {totalPrice > 0 ? (
                <span className="menu-item-modal-add-price">{` $${totalPrice}`}</span>
              ) : (
                <span className="menu-item-modal-add-price"> $0.00</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CustomItemModal;
